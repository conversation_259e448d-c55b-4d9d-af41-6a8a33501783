/* css/global.css */

/* Import Inter font */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

:root {
  /* NEW Color Palette - Based on your Coolors export */
  --primary-accent: #5a5dd1; /* <PERSON> Blue - for primary actions, highlights */
  --primary-accent-darker: #3b3a8f; /* <PERSON> Blue - for hover, darker shades */
  --background-main: #f5faff; /* Alice Blue 2 - main page background */
  --background-card: #f1f9ff; /* <PERSON> Blue - for cards, lighter elements */
  --text-primary: #232181; /* Resolution Blue - for main text, headings */
  --text-secondary: #3b3a8f; /* <PERSON> Blue - for secondary text, descriptions */
  --border-color: #a8a9ac; /* A neutral light gray for borders, can be adjusted */
  --border-color-strong: #5a5dd1; /* Savoy Blue for focused borders */
  --white: #ffffff;

  /* Status Colors */
  --success-bg: #e8f5e9;
  --success-text: #2e7d32;
  --success-border: #a5d6a7;
  --error-bg: #ffebee;
  --error-text: #c62828;
  --error-border: #ef9a9a;

  /* Typography */
  --font-family-sans: "Inter", sans-serif;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing & Sizing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Borders */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Basic reset and body styling */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-sans);
  background-color: var(--background-main);
  color: var(--text-primary);
  line-height: 1.6;
  font-weight: var(--font-weight-regular);
}

/* General link styling */
a {
  color: var(--primary-accent);
  text-decoration: none;
  transition: color var(--transition-fast);
}
a:hover {
  color: var(--primary-accent-darker);
  text-decoration: underline;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

/* === Buttons === */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-medium);
  font-size: 0.95rem;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  transition-property: background-color, box-shadow, transform;
  transition-duration: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}
.btn:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}
.btn:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background-color: var(--primary-accent); /* UPDATED */
  color: var(--white);
}
.btn-primary:hover {
  background-color: var(--primary-accent-darker); /* UPDATED */
}

.btn-secondary {
  background-color: var(--background-card); /* UPDATED */
  color: var(--primary-accent); /* UPDATED */
  border: 1px solid var(--primary-accent); /* UPDATED - border with accent */
}
.btn-secondary:hover {
  background-color: var(
    --background-main
  ); /* UPDATED - slightly different light bg for hover */
  color: var(--primary-accent-darker); /* UPDATED */
}

.btn-danger {
  background-color: #ef4444; /* Standard Red */
  color: var(--white);
}
.btn-danger:hover {
  background-color: #dc2626; /* Darker Red */
}

.btn-link {
  background-color: transparent;
  color: var(--primary-accent); /* UPDATED */
  padding: var(--spacing-xs) var(--spacing-sm);
  box-shadow: none;
  font-weight: var(--font-weight-medium);
}
.btn-link:hover {
  color: var(--primary-accent-darker); /* UPDATED */
  text-decoration: underline;
  transform: none;
  box-shadow: none;
}
.btn:disabled,
.btn[disabled] {
  background-color: var(
    --border-color
  ) !important; /* UPDATED - using neutral gray */
  color: var(
    --text-secondary
  ) !important; /* UPDATED - using secondary text, slightly darker */
  opacity: 0.7; /* Dim disabled state */
  cursor: not-allowed;
  box-shadow: none !important;
  transform: none !important;
}

/* === Cards === */
.card {
  background-color: var(--background-card); /* UPDATED */
  border: 1px solid var(--border-color); /* UPDATED - using neutral border */
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary); /* UPDATED */
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast);
}
.card:hover {
  box-shadow: var(--shadow-md);
}
.card-header {
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color); /* UPDATED */
}
.card-title {
  font-size: 1.1em;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary); /* UPDATED */
  margin: 0;
}

/* === Form Inputs & Selects === */
input[type="text"],
input[type="search"],
select,
textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color); /* UPDATED */
  border-radius: var(--border-radius-md);
  background-color: var(--white); /* Inputs usually have white background */
  color: var(--text-primary); /* UPDATED */
  font-family: var(--font-family-sans);
  font-size: 0.95rem;
  transition: border-color var(--transition-fast),
    box-shadow var(--transition-fast);
}
input[type="text"]:focus,
input[type="search"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--border-color-strong); /* UPDATED */
  box-shadow: 0 0 0 2px rgba(90, 93, 209, 0.2); /* Focus ring with new primary accent */
}
textarea {
  min-height: 80px;
  resize: vertical;
}
label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary); /* UPDATED */
}

/* === Toggle Switches (Checkboxes) === */
input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color); /* UPDATED */
  border-radius: var(--border-radius-sm);
  vertical-align: middle;
  cursor: pointer;
  position: relative;
  transition: background-color var(--transition-fast),
    border-color var(--transition-fast);
}
input[type="checkbox"]:checked {
  background-color: var(--primary-accent); /* UPDATED */
  border-color: var(--primary-accent); /* UPDATED */
}
input[type="checkbox"]:checked::after {
  content: "";
  display: block;
  width: 5px;
  height: 10px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg) translate(-50%, -50%);
  position: absolute;
  left: 50%;
  top: 45%;
}
label input[type="checkbox"] {
  margin-right: var(--spacing-sm);
}

/* === Utility Classes === */
.hidden {
  display: none !important;
}
.text-center {
  text-align: center;
}
.mt-1 {
  margin-top: var(--spacing-sm);
}
.mb-1 {
  margin-bottom: var(--spacing-sm);
}
.p-1 {
  padding: var(--spacing-sm);
}
