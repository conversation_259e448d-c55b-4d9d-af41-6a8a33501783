/* css/popup.css */

/* Ensure global.css is linked first in your popup.html for variables and base styles */

body {
  font-family: var(--font-family-sans);
  background-color: var(--background-main);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  width: 320px; /* Adjusted width based on your reference image proportions */
  min-height: 300px;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-container {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 0;
}

.popup-header {
  display: flex; /* For logo and title alignment */
  align-items: center;
  justify-content: center; /* Center if logo is small or just title */
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg); /* More space for prominent save button */
  padding-bottom: 0; /* No border from reference */
  border-bottom: none;
  flex-shrink: 0;
}

/* Add .header-logo style if you included it in popup.html */
.header-logo {
  width: 32px; /* Adjust as needed */
  height: 32px;
}

.popup-header h1 {
  font-size: 1.4em; /* Adjust to match reference */
  color: var(--text-primary); /* From your new palette */
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.popup-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg); /* Space between save button and sessions section */
  flex-grow: 1;
  overflow: hidden;
  min-height: 0;
}

/* Quick Actions & Save Form */
.quick-actions {
  flex-shrink: 0;
}
.quick-actions .btn.btn-primary.btn-full-width {
  /* Target the specific button */
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md); /* 0.6rem 1rem from reference was good */
  font-size: 0.95rem; /* Match reference button text size */
  border-radius: var(
    --border-radius-lg
  ); /* More rounded corners as per reference */
  font-weight: var(--font-weight-semibold); /* Bolder button text */
  /* .btn and .btn-primary styles from global.css will provide base colors and hover */
}

#saveSessionForm.card {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md); /* More gap inside the form */
}
#saveSessionForm input[type="text"] {
  margin-bottom: 0; /* Let gap handle spacing */
}
#saveSessionForm .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* Sessions Section (for "Recent Sessions") */
.sessions-section {
  /* Was .sessions-container */
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 0;
}
.section-title {
  /* Was .sessions-container .sessions-title */
  font-size: 1.1em; /* Or adjust to match reference */
  font-weight: var(--font-weight-bold); /* Bolder "Recent Sessions" */
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  flex-shrink: 0;
  border-bottom: none; /* No border under "Recent Sessions" in reference */
  padding-bottom: 0;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm); /* Space between cards */
  flex-grow: 1;
  overflow-y: auto;
  padding: 2px; /* Minimal padding for card shadows/edges if any */
  /* margin-right: calc(var(--spacing-xs) * -1); Removed for cleaner border handling */
  background-color: transparent; /* List itself has no background, cards do */
  border: none; /* List itself has no border */
  border-radius: 0;
}

/* === Individual Session Item Card - STYLED TO MATCH YOUR PREFERRED LAYOUT + REFERENCE AESTHETIC === */
.session-item-card {
  /* This class comes from your JS */
  background-color: var(--white);
  border: 1px solid #e5e7eb; /* Subtle border for the card */
  border-radius: var(--border-radius-lg); /* From reference: 0.75rem */
  padding: var(--spacing-sm) var(--spacing-md); /* e.g., 8px 16px */
  margin-bottom: var(--spacing-sm); /* If .sessions-list has no gap */
  /* No hover effect directly on card per reference, buttons have hover */
}
.session-item-card:last-child {
  margin-bottom: 0; /* If list has no gap */
}

.session-item-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline; /* Align title and date by their text baseline */
  margin-bottom: var(--spacing-xs);
}

.session-title-text {
  /* Comes from your JS */
  font-size: 0.95rem; /* Or 1rem for more prominence */
  font-weight: var(--font-weight-semibold); /* Make title bold */
  color: var(--text-primary);
  margin-right: var(--spacing-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.session-date-text {
  /* Comes from your JS */
  font-size: 0.75rem;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.session-item-body {
  /* Container for stats and actions */
  display: flex;
  justify-content: space-between;
  align-items: center; /* Vertically center stats and actions */
  margin-top: var(--spacing-xs); /* Small gap from header */
}

.session-meta-text {
  /* Comes from your JS */
  font-size: 0.8rem;
  color: var(--text-secondary); /* Lighter/secondary color */
  /* The (X groups) part will inherit this. You can style it with a span if needed */
}

.session-actions-inline {
  /* Comes from your JS */
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

/* Button styles for within the session card - these are the small buttons */
.btn.btn-restore {
  /* JS should add 'btn' and 'btn-restore' */
  background-color: var(--primary-accent); /* As per your actual image */
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-md); /* Adjust padding to match screenshot */
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-md); /* Match rounding in screenshot */
  border: none;
  line-height: 1.4; /* For text centering */
  /* Hover from .btn-primary in global.css will apply if .btn-primary is also used,
       otherwise define hover here: */
}
.btn.btn-restore:hover {
  background-color: var(--primary-accent-darker);
}

.btn.btn-delete {
  /* JS should add 'btn' and 'btn-delete' */
  background-color: #fee2e2; /* Light red background from reference */
  color: #ef4444; /* Red text from reference */
  padding: var(--spacing-xs) var(--spacing-md); /* Match restore button padding */
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-md); /* Match rounding */
  border: 1px solid transparent; /* Or #FECACA for a very subtle border */
  line-height: 1.4;
}
.btn.btn-delete:hover {
  background-color: #fecaca; /* Slightly darker light red */
  color: #dc2626; /* Darker red text */
}

/* Empty State - (Using classes from your JS) */
.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-secondary);
  padding: var(--spacing-xl) var(--spacing-md);
  flex-grow: 1;
}
.empty-state-icon {
  width: 48px;
  height: 48px;
  fill: var(--text-secondary);
  opacity: 0.6; /* Slightly more subtle icon */
  margin-bottom: var(--spacing-md);
}
.empty-state-content p {
  font-size: 0.95rem;
}
.empty-state-content.error p {
  color: var(--error-text);
}

/* Footer */
.popup-footer {
  margin-top: auto;
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-around; /* Use space-around for even distribution */
  align-items: center;
  font-size: 0.85em;
  flex-shrink: 0;
  padding-bottom: var(--spacing-sm);
  padding-left: var(--spacing-sm); /* Add some horizontal padding to footer */
  padding-right: var(--spacing-sm);
}
.footer-link {
  /* This is an <a> tag from HTML */
  display: flex; /* Added for icon-text alignment */
  align-items: center; /* Added for icon-text alignment */
  color: var(--text-secondary);
  text-decoration: none;
  padding: var(--spacing-xs); /* Minimal padding around text */
  flex-grow: 1; /* Allow items to grow and fill space if using space-around */
  border-radius: var(--border-radius-sm);
  transition: color var(--transition-fast),
    background-color var(--transition-fast);
  font-weight: var(
    --font-weight-medium
  ); /* Make footer links slightly bolder */
}

.footer-link svg {
  width: 1em; /* Relative to font size of link */
  height: 1em;
  margin-right: var(--spacing-xs); /* Space between icon and text */
  vertical-align: -0.125em; /* Fine-tune vertical alignment */
  fill: currentColor; /* SVG inherits color from parent link */
}

.footer-link:hover {
  color: var(--primary-accent);
  background-color: var(--background-card);
  text-decoration: none;
}

/* Scrollbar styling */
.sessions-list::-webkit-scrollbar {
  width: 5px;
}
.sessions-list::-webkit-scrollbar-track {
  background: transparent;
}
.sessions-list::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: var(--border-radius-md);
}
.sessions-list::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-accent);
}
