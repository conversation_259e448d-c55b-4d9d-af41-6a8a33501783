/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f9f9f9;
  height: 100vh;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Header styles */
header {
  background-color: #4285f4;
  color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

header h1 {
  font-size: 24px;
  font-weight: 500;
}

.search-container {
  width: 300px;
}

#searchInput {
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

#searchInput::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Main content styles */
main {
  display: flex;
  flex: 1;
  overflow: hidden;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 20px;
}

/* Sidebar styles */
.sidebar {
  width: 250px;
  padding-right: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.actions-panel,
.filters-panel {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.actions-panel {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filters-panel h3 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  color: #666;
}

.filter-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

/* Content styles */
.content {
  flex: 1;
  overflow-y: auto;
  padding-left: 20px;
}

.sessions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.sessions-header h2 {
  font-size: 20px;
  color: #333;
}

.view-options {
  display: flex;
  gap: 5px;
}

.view-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
  cursor: pointer;
  color: #777;
}

.view-btn.active {
  background-color: #f1f1f1;
  color: #333;
}

/* Sessions grid view */
.sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

/* Sessions list view */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* List item group indicators */
.list-item-groups {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 8px;
}

.list-group-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  color: white;
}

.list-group-indicator.ungrouped {
  background-color: #888;
}

.group-name {
  max-width: 80px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-count {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 1px 5px;
  font-size: 10px;
}

/* Session card styles */
.session-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.session-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.card-title {
  font-weight: 500;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-date {
  font-size: 12px;
  color: #777;
}

.card-body {
  padding: 15px;
}

.card-stats {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 13px;
  color: #555;
}

.card-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.group-preview,
.ungrouped-preview {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.group-preview {
  border-left: 3px solid;
}

.group-preview-header,
.ungrouped-preview-header {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 5px;
}

.group-preview-name,
.ungrouped-preview-name {
  font-weight: 500;
}

.group-preview-count,
.ungrouped-preview-count {
  color: #777;
}

.group-preview-favicons,
.ungrouped-preview-favicons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tab-preview {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  background-color: #f1f1f1;
  overflow: hidden;
}

.tab-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #777;
  background-color: #eee;
}

.empty-preview {
  padding: 10px;
  text-align: center;
  color: #777;
  font-style: italic;
}

.card-actions {
  padding: 10px 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
}

/* Button styles */
.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn.primary {
  background-color: #4285f4;
  color: white;
}

.btn.primary:hover {
  background-color: #3367d6;
}

.btn.secondary {
  background-color: #f1f1f1;
  color: #333;
}

.btn.secondary:hover {
  background-color: #e4e4e4;
}

.btn.danger {
  background-color: #ea4335;
  color: white;
}

.btn.danger:hover {
  background-color: #d33426;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #777;
  grid-column: 1 / -1;
}

.empty-state p {
  margin-bottom: 15px;
  font-size: 16px;
}

/* Footer styles */
footer {
  padding: 15px 20px;
  background-color: #f1f1f1;
  border-top: 1px solid #ddd;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

footer a {
  color: #4285f4;
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal.hidden {
  display: none;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #777;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.session-meta {
  margin-bottom: 15px;
  color: #555;
}

.session-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tabs-container h3 {
  margin-bottom: 10px;
  font-size: 16px;
}

.tabs-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Tab group styles */
.tab-group {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.group-header-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.group-name {
  font-weight: 500;
}

.group-count {
  font-size: 12px;
  color: #666;
}

.group-actions {
  display: flex;
  gap: 5px;
}

.group-expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.group-expand-btn.collapsed {
  transform: rotate(-90deg);
}

.group-tabs {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background-color: white;
  transition: max-height 0.3s;
  overflow: hidden;
}

.group-tabs.collapsed {
  max-height: 0;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  border-bottom: 1px solid #f5f5f5;
  background-color: white;
}

.tab-item:last-child {
  border-bottom: none;
}

.tab-favicon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.tab-info {
  flex: 1;
  min-width: 0; /* Needed for text-overflow to work in a flex child */
  display: flex;
  flex-direction: column;
}

.tab-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-url {
  color: #777;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-open-link {
  color: #4285f4;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-open-link:hover {
  background-color: #f1f1f1;
}

/* Ungrouped tabs header */
.group-header.ungrouped {
  background-color: #f0f0f0;
}

/* Form styles */
#saveSessionForm {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
  background-color: #f1f1f1;
  border-radius: 4px;
}

#saveSessionForm.hidden {
  display: none;
}

#sessionName {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
}

/* Session notes styles */
.session-notes {
  margin: 20px 0;
}

.session-notes h3 {
  margin-bottom: 10px;
  font-size: 16px;
}

.notes-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

#sessionNotes {
  width: 100%;
  min-height: 100px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

#saveNotesBtn {
  align-self: flex-end;
  padding: 8px 15px;
}
