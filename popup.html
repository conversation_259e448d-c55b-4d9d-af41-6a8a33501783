<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TabNest</title>
    <link rel="stylesheet" href="css/global.css" />
    <link rel="stylesheet" href="css/popup.css" />
  </head>
  <body>
    <div class="popup-container">
      <header class="popup-header">
        <img src="icons/icon48.png" alt="TabNest Logo" class="header-logo" />
        <h1 class="header-title">TabNest</h1>
      </header>

      <main class="popup-main">
        <div class="quick-actions">
          <button id="saveTabsBtn" class="btn btn-primary btn-save-tabs">
            <!-- Icon will be added via CSS pseudo-element or inline SVG if preferred -->
            Save Current Tabs
          </button>
          <div id="saveSessionForm" class="hidden card">
            <input type="text" id="sessionName" placeholder="Session name" />
            <div class="form-actions">
              <button id="confirmSaveBtn" class="btn btn-primary btn-small">
                Save
              </button>
              <button id="cancelSaveBtn" class="btn btn-secondary btn-small">
                Cancel
              </button>
            </div>
          </div>
        </div>

        <div class="sessions-section">
          <div class="section-header">
            <h2 class="section-title">Recent Sessions</h2>
          </div>
          <div id="sessionsList" class="sessions-list">
            <!-- Session items are dynamically generated by popup.js -->
            <!-- Example structure for JS to generate:
            <div class="session-card" data-id="[SESSION_ID]">
              <div class="session-card-header">
                <h3 class="session-card-title">[Session Name]</h3>
                <span class="session-card-date">[Date]</span>
              </div>
              <p class="session-card-meta">[Tab/Group Count]</p>
              <div class="session-card-actions">
                <button class="btn btn-icon btn-restore restore-btn" data-id="[SESSION_ID]" title="Restore Session">
                  <span class="btn-icon-svg">↩️</span> Restore
                </button>
                <button class="btn btn-icon btn-delete delete-btn" data-id="[SESSION_ID]" title="Delete Session">
                  <span class="btn-icon-svg">🗑️</span> Delete
                </button>
              </div>
            </div>
            -->
          </div>
        </div>
      </main>

      <footer class="popup-footer">
        <a href="#" id="exportBtn" class="footer-link" title="Export Sessions">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" y1="3" x2="12" y2="15"></line>
          </svg>
          Export
        </a>
        <a href="#" id="importBtn" class="footer-link" title="Import Sessions">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          Import
        </a>
        <a href="#" id="settingsLinkPopup" class="footer-link" title="Settings">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="12" cy="12" r="3"></circle>
            <path
              d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
            ></path>
          </svg>
          Settings
        </a>
        <a
          href="dashboard.html"
          target="_blank"
          id="manageAllLink"
          class="footer-link"
          title="Open Dashboard"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <rect x="3" y="3" width="7" height="7"></rect>
            <rect x="14" y="3" width="7" height="7"></rect>
            <rect x="14" y="14" width="7" height="7"></rect>
            <rect x="3" y="14" width="7" height="7"></rect>
          </svg>
          Dashboard
        </a>
      </footer>
    </div>
    <script src="js/popup.js"></script>
  </body>
</html>
